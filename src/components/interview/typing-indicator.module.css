.typingContainer {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typingDot {
  animation: blink 1.4s infinite both;
  background-color: currentColor;
  border-radius: 50%;
  display: inline-block;
  height: 4px;
  width: 4px;
  opacity: 0.5;
}

.typingDot:nth-child(2) {
  animation-delay: 0.2s;
}

.typingDot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes blink {
  0% {
    opacity: 0.1;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.1;
    transform: scale(0.8);
  }
}
